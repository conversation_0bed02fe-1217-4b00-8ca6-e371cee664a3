<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Department extends Model
{
    protected $connection = 'sqlsrv';
    protected $table = 'Departments';
    protected $primaryKey = 'Department_ID';
    public $timestamps = false;

    protected $fillable = [
        'Department_ID',
        'Department_Desc',
        'Department_Code',
    ];

    public function products()
    {
        return $this->hasMany(Product::class, 'Department_ID');
    }
}
