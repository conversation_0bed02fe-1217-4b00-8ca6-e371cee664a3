<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Part extends Model
{
    use HasFactory;

    protected $table = 'parts';
    protected $primaryKey = 'PRODUCT_ID';
    protected $connection = 'datalogic';
    public $timestamps = false;
    public $incrementing = false;

    protected $fillable = [
        'PRODUCT_ID',
        'IMAGE'
    ];



    public function getRelationshipsAttribute()
    {
        return $this->belongsTo(Product::class,'part_number','product_id');
    }

    /**
     * Get the image URL for this part
     * Handles various image path formats from different applications
     */
    public function getImageUrlAttribute()
    {
        if (!$this->IMAGE) {
            return '/assets/default-product.png';
        }

        // If it's already a full URL, return as is
        if (filter_var($this->IMAGE, FILTER_VALIDATE_URL)) {
            return $this->IMAGE;
        }

        // If it starts with '/', it's an absolute path from public root
        if (substr($this->IMAGE, 0, 1) === '/') {
            return $this->IMAGE;
        }

        // Handle Windows-style paths
        if (preg_match('/^[A-Za-z]:\\\\/', $this->IMAGE)) {
            $filename = basename($this->IMAGE);
            return "/storage/product-images/{$filename}";
        }

        // If it contains storage path indicators
        if (strpos($this->IMAGE, 'storage/') !== false || strpos($this->IMAGE, 'public/') !== false) {
            return \Illuminate\Support\Facades\Storage::url($this->IMAGE);
        }

        // Default to storage URL
        return \Illuminate\Support\Facades\Storage::url($this->IMAGE);
    }
}
