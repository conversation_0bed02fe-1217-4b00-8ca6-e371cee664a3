<?php

namespace App\Http\Controllers;

use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class DashboardController extends Controller
{
    public function index()
    {
        Log::info('DashboardController@index accessed', [
            'user_id' => Auth::check() ? Auth::user()->getKey() : null,
            'is_authenticated' => Auth::check()
        ]);
        
        // Check if user is authenticated
        if (!Auth::check()) {
            Log::warning('Dashboard access denied: not authenticated');
            return redirect()->route('login');
        }

        $user = Auth::user();
        Log::info('Dashboard loaded for user', [
            'user_id' => $user->getKey(),
            'user_name' => $user->NAME ?? null
        ]);

        try {
            
            $products = Product::with('department')
                ->active() // Only get non-delisted products
                ->get()
                ->map(function ($product) {
                    return [
                        'id' => $product->Product_ID,
                        'name' => $product->Product_Desc,
                        'description' => $product->Product_Desc, // You might want to add a separate description field
                        'price' => (float) $product->Product_Price,
                        'category' => $product->department->Department_Desc ?? 'Uncategorized',
                        'image' => null, // You'll need to add image field to your Product model if needed
                        'available' => $product->isActive(),
                        'printer_location' => $product->PrinterLocation,
                        'printer_location_desc' => $product->getPrinterLocationDescriptionAttribute(),
                        'quick_code' => $product->Product_Quick_Code,
                        'serving' => $product->Serving,
                        'is_group_meal' => (bool) $product->isGroupMeal,
                    ];
                });

            Log::info('Products fetched successfully', [
                'product_count' => $products->count()
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching products', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // Return empty products array if there's an error
            $products = collect([]);
        }

        return Inertia::render('Dashboard', [
            'user' => [
                'ID' => $user->ID ?? $user->Cashier_ID ?? null,
                'NUMBER' => $user->NUMBER ?? $user->Cashier_Number ?? null,
                'NAME' => $user->NAME ?? $user->Cashier_Name ?? null,
                'LEVEL' => $user->LEVEL ?? $user->Cashier_Role_ID ?? null,
            ],
            'products' => $products
        ]);
    }

   
    public function getProducts()
    {
        try {
            $products = Product::with('department')
                ->active()
                ->get()
                ->map(function ($product) {
                    return [
                        'id' => $product->Product_ID,
                        'name' => $product->Product_Desc,
                        'description' => $product->Product_Desc,
                        'price' => (float) $product->Product_Price,
                        'category' => $product->department->Department_Desc ?? 'Uncategorized',
                        'image' => null,
                        'available' => $product->isActive(),
                        'printer_location' => $product->PrinterLocation,
                        'printer_location_desc' => $product->getPrinterLocationDescriptionAttribute(),
                        'quick_code' => $product->Product_Quick_Code,
                        'serving' => $product->Serving,
                        'is_group_meal' => (bool) $product->isGroupMeal,
                    ];
                });

            return response()->json($products);

        } catch (\Exception $e) {
            Log::error('Error fetching products via API', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(['error' => 'Failed to fetch products'], 500);
        }
    }
}