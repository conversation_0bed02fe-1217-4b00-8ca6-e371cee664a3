<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class OrderSlipDetail extends Model
{

    protected $table = 'OrderSlipDetails';
    protected $connection = 'datalogic';
    public $timestamps = false;
    public $incrementing = false;

    protected $fillable = [
        'OSNUMBER',
        'ORDE<PERSON><PERSON><PERSON>N<PERSON>',
        'ORDE<PERSON><PERSON><PERSON>DETAILID',
        'BRANCH<PERSON>',
        'OUTLETID',
        'DEVICEN<PERSON>',
        'PRODUCT_ID',
        'RETAILPRICE',
        'QUANTITY',
        'REQUESTEDQTY',
        'AMOUNT',
        'NETAMOUNT',
        'REMARKS',
        'PARTNO',
        'LINE_NO',
        'ORNO',
        'OSTYPE',
        'STATUS',
        'SEQUENCE',
        'OSDATE',
        'LOCATIONID',
        'ENCODEDDATE',
        'DISPLAYMONITOR',
        'POSLINENO',
        'MAIN_PRODUCT_ID',
        'MAIN_PRODUCT_COMPONENT_ID',
        'MAIN_PRODUCT_COMPONENT_QTY',
        'CUSTOMERCODE',
        'DEV_MOD',
        'GUESTN<PERSON>',
        'GUEST_TYPE',
        'P<PERSON><PERSON><PERSON><PERSON>',
        'MEAL_STUB_PRODUCT_ID',
        'MEAL_STUB_SERIAL_NUMBER',
        'TABLENO',
        'OS_SC_ID',
        'IS_MODIFY',
        'VATABLE_SALES',
        'VAT_AMOUNT',
        'SC_DISCOUNT_PERCENTAGE',
        'SC_DISCOUNT_AMOUNT',
        'SC_COUNT',
        'REG_COUNT',
        'VAT_EX',
        'IS_GROUP_MEAL',
        'GROUP_SERVING',
        'CONFIRMED_AT',
        'PRODUCTGROUP',
        'PSTATUS',
        'IS_TAX_EXEMPT',
       
    ];

     public function product()
    {
        return $this->belongsTo(Product::class, 'PRODUCT_ID', 'Product_ID');
    }

 




    public function header()
    {
        return $this->belongsTo(OrderSlipHeader::class, 'OSNUMBER', 'OSNUMBER');
    }

    /**
     * Accessors
     */
    public function getFormattedAmountAttribute()
    {
        return number_format($this->AMOUNT, 2);
    }

    public function getFormattedNetAmountAttribute()
    {
        return number_format($this->NETAMOUNT, 2);
    }

    /**
     * Logic
     */
    public static function getNewDetailId($orderslip_number)
    {
        $result = static::where('OSNUMBER', $orderslip_number)
            ->orderBy('LINE_NO', 'desc')
            ->first();

        return is_null($result) ? 1 : $result->ORDERSLIPDETAILID + 1;
    }

    public static function getNewProductSequence($orderslip_number, $product_id)
    {
        $result = static::where('OSNUMBER', $orderslip_number)
            ->where('MAIN_PRODUCT_ID', $product_id)
            ->orderBy('SEQUENCE', 'desc')
            ->first();

        return is_null($result) ? 1 : $result->SEQUENCE + 1;
    }

    public static function getNewLineNumber($orderslip_number)
    {
        $result = static::where('OSNUMBER', $orderslip_number)
            ->orderBy('LINE_NO', 'desc')
            ->first();

        return is_null($result) ? 1 : $result->LINE_NO + 1;
    }

    /**
     * Scope for filtering by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('STATUS', $status);
    }

    /**
     * Scope for filtering by branch.
     */
    public function scopeByBranch($query, $branchId)
    {
        return $query->where('BRANCHID', $branchId);
    }                                          
}

