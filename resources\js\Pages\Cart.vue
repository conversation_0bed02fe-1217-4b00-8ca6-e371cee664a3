<template>
    <div class="min-h-screen bg-gray-100">
        <!-- Navigation -->
        <nav class="bg-white shadow sticky top-0 z-10">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center space-x-4">
                        <Button @click="goToDashboard" severity="secondary" outlined>
                            <i class="pi pi-arrow-left mr-2"></i>
                            Back to Menu
                        </Button>
                        <!-- <h1 class="text-xl font-semibold text-gray-900">Shopping Cart</h1> -->
                    </div>
                    <div class="flex items-center space-x-4">
                        <button @click="logout"
                            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                            Logout
                        </button>
                    </div>
                </div>
            </div>
        </nav>

        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Cart Items -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-900">Your Cart</h2>
                    <Badge :value="cartItemsCount" severity="info" />
                </div>

                <!-- Cart Items List -->
                <div v-if="cartItems.length > 0" class="space-y-4">
                    <div v-for="item in cartItems" :key="item.id"
                        class="flex justify-between items-center p-4 bg-gray-50 rounded-lg border">
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-900 text-lg">{{ item.name }}</h4>
                            <p class="text-gray-600">₱{{ item.price.toFixed(2) }} each</p>
                            <p class="text-sm text-gray-500 mt-1">Subtotal: ₱{{ (item.price * item.quantity).toFixed(2) }}</p>
                        </div>
                        <div class="flex items-center space-x-3">
                            <Button icon="pi pi-minus" size="small" severity="secondary" outlined
                                @click="decreaseQuantity(item.id)" 
                                :disabled="item.quantity <= 1" />
                            <span class="font-semibold min-w-[3rem] text-center text-lg">{{ item.quantity }}</span>
                            <Button icon="pi pi-plus" size="small" severity="secondary" outlined
                                @click="increaseQuantity(item.id)" />
                            <Button icon="pi pi-trash" size="small" severity="danger" outlined
                                @click="removeItem(item.id)" 
                                class="ml-4" />
                        </div>
                    </div>

                    <!-- Cart Summary -->
                    <Divider />
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <div class="space-y-3">
                            <div class="flex justify-between text-lg">
                                <span>Items ({{ cartItemsCount }}):</span>
                                <span>₱{{ cartSubtotal.toFixed(2) }}</span>
                            </div>
                            <!-- <div class="flex justify-between text-lg">
                                <span>Tax (12%):</span>
                                <span>₱{{ cartTax.toFixed(2) }}</span>
                            </div> -->
                            <Divider />
                            <div class="flex justify-between text-2xl font-bold text-green-600">
                                <span>Total:</span>
                                <span>₱{{ cartTotal.toFixed(2) }}</span>
                            </div>
                        </div>
                        
                        <div class="mt-6 space-y-3">
                            <Button label="Proceed to Checkout" icon="pi pi-credit-card" 
                                class="w-full" severity="success" size="large"                                                                                                           
                                @click="checkout" />
                            <Button label="Clear Cart" icon="pi pi-trash" 
                                class="w-full" severity="danger" outlined
                                @click="clearCart" />
                        </div>
                    </div>
                </div>

                <!-- Empty Cart -->
                <div v-else class="text-center py-16">
                    <i class="pi pi-shopping-cart text-6xl text-gray-300 mb-6"></i>
                    <h3 class="text-2xl font-semibold text-gray-600 mb-4">Your cart is empty</h3>
                    <p class="text-gray-500 text-lg mb-8">Add some delicious items from our menu!</p>
                    <Button label="Browse Menu" icon="pi pi-arrow-left" 
                        severity="info" size="large"
                        @click="goToDashboard" />
                </div>
            </div>
        </div>                       
    </div>                
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { router } from '@inertiajs/vue3'
import Button from 'primevue/button'
import Badge from 'primevue/badge'
import Divider from 'primevue/divider'

// Define props to receive user data from the controller
const props = defineProps({
    user: {
        type: Object,
        required: true
    }
})

// Reactive data
const cartItems = ref([])

// Computed properties
const cartItemsCount = computed(() => {
    return cartItems.value.reduce((total, item) => total + item.quantity, 0)
})

const cartSubtotal = computed(() => {
    return cartItems.value.reduce((total, item) => total + (item.price * item.quantity), 0)
})

const cartTax = computed(() => {
    return cartSubtotal.value * 0.12 // 12% tax
})

const cartTotal = computed(() => {
    return cartSubtotal.value + cartTax.value
})

// Cart management functions
const loadCartFromStorage = () => {
    try {
        const savedCart = localStorage.getItem('restaurant_cart')
        if (savedCart) {
            cartItems.value = JSON.parse(savedCart)
        }
    } catch (error) {
        console.error('Error loading cart from storage:', error)
        cartItems.value = []
    }
}

const saveCartToStorage = () => {
    try {
        localStorage.setItem('restaurant_cart', JSON.stringify(cartItems.value))
    } catch (error) {
        console.error('Error saving cart to storage:', error)
    }
}

const increaseQuantity = (productId) => {
    const item = cartItems.value.find(item => item.id === productId)
    if (item) {
        item.quantity += 1
        saveCartToStorage()
    }
}

const decreaseQuantity = (productId) => {
    const item = cartItems.value.find(item => item.id === productId)
    if (item && item.quantity > 1) {
        item.quantity -= 1
        saveCartToStorage()
    }
}

const removeItem = (productId) => {
    cartItems.value = cartItems.value.filter(item => item.id !== productId)
    saveCartToStorage()
}

const clearCart = () => {
    if (confirm('Are you sure you want to clear your cart?')) {
        cartItems.value = []
        saveCartToStorage()
    }
}

const checkout = () => {
    if (cartItems.value.length === 0) {
        alert('Your cart is empty!')
        return
    }

    // Prepare order data
    const orderData = {
        items: cartItems.value,
        subtotal: cartSubtotal.value,
        tax: cartTax.value,
        total: cartTotal.value,
        user_id: props.user.id || props.user.ID
    }

    console.log('Processing order:', orderData)

    // You can use Inertia to submit the order
    router.post('/orders', orderData, {
        onSuccess: () => {
            alert('Order placed successfully!')
            clearCart()
            goToDashboard()
        },
        onError: (errors) => {
            console.error('Order failed:', errors)
            alert('Failed to place order. Please try again.')
        }
    })
}

// Navigation functions
const goToDashboard = () => {
    router.get('/dashboard')
}

const logout = () => {
    router.post('/logout', {}, {
        onSuccess: () => {
            // Redirect will be handled by the controller
        }
    })
}

// Lifecycle hooks
onMounted(() => {
    loadCartFromStorage()
})
</script>