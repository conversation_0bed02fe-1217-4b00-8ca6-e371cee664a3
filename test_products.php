<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

try {
    echo "Testing database connection...\n";
    
    $productCount = App\Models\Product::count();
    echo "Total products found: " . $productCount . "\n";
    
    $activeProducts = App\Models\Product::active()->count();
    echo "Active products found: " . $activeProducts . "\n";
    
    if ($activeProducts > 0) {
        $firstProduct = App\Models\Product::active()->first();
        echo "First product: " . $firstProduct->Product_Desc . " - $" . $firstProduct->Product_Price . "\n";
    }
    
    echo "Database connection successful!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}