<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\OrderSlipHeader;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

class CurrentTransactionController extends Controller
{
    // Get current user's attached order slip (if any, pending and not complete/paid)
    public function currentTransaction(Request $request)
    {
        $request->validate([
            'user_id' => 'required|integer',
        ]);
        try {
            $orderslip = OrderSlipHeader::where('USER_CURRENT_TRANSACTION', $request->user_id)
                ->where('PAID', 0)
                ->whereNull('QDATE')
                ->first();
            if (!$orderslip) {
                return response()->json([ 'message' => 'No active transaction.' ], 404);
            }
            return response()->json([
                'message' => 'Active transaction found.',
                'orderslip_number' => $orderslip->ORDERSLIPNO,
                'status' => $orderslip->STATUS
            ]);
        } catch (Exception $e) {
            return response()->json([
                'message' => 'SERVER ERROR',
                'system' => $e->getMessage()
            ], 500);
        }
    }

    // Detach the current user from their transaction
    public function detachCurrentTransaction(Request $request)
    {
        $request->validate([
            'orderslip_number' => 'required|integer',
            'user_id' => 'required|integer'
        ]);
        try {
            DB::beginTransaction();
            $updated = OrderSlipHeader::where('ORDERSLIPNO', $request->orderslip_number)
                ->where('USER_CURRENT_TRANSACTION', $request->user_id)
                ->update(['USER_CURRENT_TRANSACTION' => null]);
            DB::commit();
            return response()->json([
                'success' => (bool) $updated,
                'message' => $updated ? 'Detached' : 'Nothing to detach.'
            ]);
        } catch(Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'SERVER ERROR',
                'system' => $e->getMessage()
            ], 500);
        }
    }

    // Attach the current user to a specific order slip (if not already in use)
    public function attachCurrentTransaction(Request $request)
    {
        $request->validate([
            'orderslip_number' => 'required|integer',
            'user_id' => 'required|integer'
        ]);
        try {
            DB::beginTransaction();
            $orderslip = OrderSlipHeader::where('ORDERSLIPNO', $request->orderslip_number)
                ->whereNull('USER_CURRENT_TRANSACTION')
                ->first();
            if (!$orderslip) {
                DB::rollBack();
                return response()->json([
                    'success' => false,
                    'message' => 'Order slip already in use or not found.'
                ], 400);
            }
            $orderslip->USER_CURRENT_TRANSACTION = $request->user_id;
            $orderslip->save();
            DB::commit();
            return response()->json([
                'success' => true,
                'message' => 'Attached.'
            ]);
        } catch(Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'SERVER ERROR',
                'system' => $e->getMessage()
            ], 500);
        }
    }

    // Mark a transaction as complete/paid and detach user
    public function markAsComplete(Request $request)
    {
        $request->validate([
            'orderslip_number' => 'required|integer',
            'user_id' => 'required|integer'
        ]);
        try {
            DB::beginTransaction();
            $updated = OrderSlipHeader::where('ORDERSLIPNO', $request->orderslip_number)
                ->where('USER_CURRENT_TRANSACTION', $request->user_id)
                ->update([
                    'QDATE' => now(),
                    'PAID' => 1,
                    'USER_CURRENT_TRANSACTION' => null
                ]);
            DB::commit();
            return response()->json([
                'success' => (bool) $updated,
                'message' => $updated ? 'Marked as complete.' : 'Transaction not found or already completed.'
            ]);
        } catch(Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'SERVER ERROR',
                'system' => $e->getMessage()
            ], 500);
        }
    }
}
