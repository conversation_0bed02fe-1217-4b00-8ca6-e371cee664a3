<?php

namespace App\Http\Resources;

use App\Models\Part;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Log;

class ProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
     public function toArray($request)
    {
       // Resolve image even when IMAGEFULLPATH is null by checking public/assets
       $part = Part::select('IMAGEFULLPATH')->where('PRODUCT_ID', $this->Product_ID)->first();
       $parts = $part ? $part->IMAGEFULLPATH : null;
       
       $imgPath = null;

       // 1) If DB has a stored absolute/relative path, try to extract "/assets/..."
       if ($parts) {
           $normalized = str_replace('\\', '/', $parts);
           $pos = stripos($normalized, '/assets/');
           if ($pos === false) {
               $pos = stripos($normalized, 'assets/');
           }
           if ($pos !== false) {
               $imgPath = '/' . ltrim(substr($normalized, $pos), '/');
           }
       }

       // 2) If still not found (IMAGEFULLPATH missing or doesn't include assets),
       //    attempt to match a file in public/assets using common fields
       if (!$imgPath) {
           $bases = [];
           $desc = trim((string) ($this->Product_Desc ?? ''));
           $quick = trim((string) ($this->Product_Quick_Code ?? ''));
           $idStr = isset($this->Product_ID) ? (string) $this->Product_ID : '';

           if ($desc !== '') {
               $bases[] = $desc;
               $bases[] = strtoupper($desc);
               $bases[] = strtolower($desc);
               $bases[] = ucwords(strtolower($desc));
           }
           if ($quick !== '') {
               $bases[] = $quick;
               $bases[] = strtoupper($quick);
               $bases[] = strtolower($quick);
               $bases[] = ucwords(strtolower($quick));
           }
           if ($idStr !== '') {
               $bases[] = $idStr;
           }

           // Remove duplicates while preserving order
           $bases = array_values(array_unique(array_filter($bases)));

           $extensions = ['jpg', 'jpeg', 'png', 'webp'];
           foreach ($bases as $base) {
               // Normalize spaces
               $base = trim(preg_replace('/\s+/', ' ', $base));
               foreach ($extensions as $ext) {
                   $relative = 'assets/' . $base . '.' . $ext;
                   if (file_exists(public_path($relative))) {
                       $imgPath = '/' . $relative;
                       break 2; // stop both loops
                   }
               }
           }
       }

        Log::info('Parts', [
            'parts' => $imgPath
        ]);
        return [
            'product_id' => $this->Product_ID,
            'product_description' => trim($this->Product_Desc),
            'price' => (double) $this->Product_Price,
            'quick_code' => trim($this->Product_Quick_Code),
            'tax_id' => $this->Tax_ID,
            'department_id' => $this->Department_ID,
            'tax_exempt_qty' => $this->Dept_Tax_Exempt_Qty,
            'sc_vat' => $this->SCVat,
            'discount_rate' => (double) $this->Discount_Rate,
            'delisted' => (bool) $this->Delisted,
            'is_group_meal' => (bool) $this->isGroupMeal,
            'serving' => $this->Serving,
            'printer_location' => $this->PrinterLocation, // Add printer location
            'img_path' => $this->when($imgPath,$imgPath , '/assets/default-product.png'),
            'is_vatable' => $this->Tax_ID ? true : false,
            'is_less_vat_for_scpwd' => $this->is_less_vat_for_scpwd ?? false,
            'sc_discount_percentage' => $this->Discount_Rate ?? 0,
        ];
    }
}
