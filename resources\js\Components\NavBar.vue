<template>
    <nav class="bg-white shadow sticky top-0 z-10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center space-x-4">
                    <Button @click="$emit('nav-table')">Table</Button>
                    <Button @click="$emit('nav-orderslips')">Orderslip</Button>
                    <Button @click="$emit('nav-dashboard')">Dashboard</Button>
                    <Button @click="$emit('nav-cart')" severity="info">
                        <i class="pi pi-shopping-cart mr-2"></i>
                        Cart
                        <Badge v-if="cartItemsCount > 0" :value="cartItemsCount" severity="danger" class="ml-2" />
                    </Button>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- <span class="text-gray-700">Welcome, {{ user.NAME }}</span> -->
                    <button @click="$emit('logout')"
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                        Logout
                    </button>
                </div>
            </div>
        </div>
    </nav>
</template>

<script setup>
import { defineProps } from 'vue'
import Button from 'primevue/button'
import Badge from 'primevue/badge'

defineProps({
    user: {
        type: Object,
        required: true,
    },
    cartItemsCount: {
        type: Number,
        default: 0,
    },
})
</script>
