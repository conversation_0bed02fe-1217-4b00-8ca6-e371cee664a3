
<template>
  <div class="min-h-full flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <Head title="Login" />
    <div class="flex justify-between sm:mx-auto sm:w-full sm:max-w-md ">
      <div class="flex items-center justify-center">
        <div class="text-sky-600 leading-4 font-semibold">
          <div class="font-normal italic">Application</div>
        </div>
      </div>
      <h1 class="mt-4 text-center text-3xl font-extrabold text-gray-700">
        Loginasdasdsa
      </h1>
    </div>

    <div class="mt-1 sm:mx-auto sm:w-full sm:max-w-md">
      <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
        <form @submit.prevent="btnSignin" class="space-y-6">
          <div>
            <label for="username" class="block text-sm font-medium text-gray-700">
              Username
            </label>
            <div class="mt-1">
              <input
                id="username"
                v-model="form.username"
                :disabled="form.processing"
                required
                autocomplete="username"
                class="appearance-none block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-sky-500 focus:border-sky-500 sm:text-sm"
                :class="form.errors.username ? 'border-red-300' : 'border-gray-300'"
              >
            </div>
            <div v-if="form.errors.username" class="mt-1 text-sm text-red-600">
              {{ form.errors.username }}
            </div>
          </div>

          <div>
            <label for="password" class="block text-sm font-medium text-gray-700">
              Password
            </label>
            <div class="mt-1">
              <input
                id="password"
                v-model="form.password"
                :disabled="form.processing"
                type="password"
                required
                autocomplete="current-password"
                class="appearance-none block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-sky-500 focus:border-sky-500 sm:text-sm"
                :class="form.errors.password ? 'border-red-300' : 'border-gray-300'"
              >
            </div>
            <div v-if="form.errors.password" class="mt-1 text-sm text-red-600">
              {{ form.errors.password }}
            </div>
          </div>

          <div>
            <button
              type="submit"
              :disabled="form.processing"
              class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500"
              :class="form.processing ? 'bg-sky-400 cursor-not-allowed' : 'bg-sky-600 hover:bg-sky-700'"
            >
              <span v-if="form.processing" class="mr-2">
                <svg class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </span>
              {{ form.processing ? 'Signing in...' : 'Sign in' }}
            </button>
          </div>
        </form>

        <div class="mt-6">
          <div class="relative">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-gray-300"></div>
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-2 bg-white text-gray-500">
                Device Information
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useForm } from '@inertiajs/vue3'
import { Head } from '@inertiajs/vue3'

// Inertia form for login
const form = useForm({
  username: '',
  password: '',
})

// Handle sign in
const btnSignin = () => {
  form.post('/login', {
    onFinish: () => {
      form.reset('password')
    },

    onError: (errors) => {
      // Optionally handle errors here
      // console.log('Login errors:', errors)
    },
  })
}
</script>

