<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CashierRole extends Model
{
     protected $table = 'Cashier_Roles';
    protected $primaryKey = 'Cashier_Role_ID';
    protected $connection = 'datalogic';

    protected $fillable = [
        'Cashier_Role_ID',
        'Role_Name'
    ];

    public function cashiers()
    {
        return $this->hasMany(User::class, 'Cashier_Role_ID', 'Cashier_Role_ID');
    }
}
