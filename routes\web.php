<?php

use App\Http\Controllers\LoginController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\OrderSlipController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use Inertia\Response;

Route::middleware('guest')->group(function (): void {
    Route::get('/', [LoginController::class, 'index'])->name('login');
    Route::post('/login', [LoginController::class, 'login']);
});

Route::middleware('auth')->group(function (): void {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::post('/logout', [LoginController::class, 'logout'])->name('logout');
    Route::get('/products', [ProductController::class, 'index'])->name('products.index');

    Route::get('/cart', function (): Response { 
        return Inertia::render('Cart'); 
    })->name('cart');

    // Page route
    Route::get('/pending-orderslips', [OrderSlipController::class, 'pendingOrderslipsPage'])->name('pending-orderslips');

    // OrderSlip API routes
    Route::prefix('api')->group(function () {
        Route::get('/orderslips', [OrderSlipController::class, 'index']);
        Route::post('/orderslips', [OrderSlipController::class, 'store']);
        Route::get('/orderslips/{orderslip_code}', [OrderSlipController::class, 'show']);
        Route::post('/orderslips/attach', [OrderSlipController::class, 'attachCurrentTransaction']);
        Route::post('/orderslips/detach', [OrderSlipController::class, 'detachCurrentTransaction']);
        Route::post('/orderslips/complete', [OrderSlipController::class, 'markAsComplete']);
        
    });
});

require __DIR__.'/auth.php';