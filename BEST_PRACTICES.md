# 📘 Project Best Practices

#### 1. Project Purpose  
This project is a Restaurant Management System built with Laravel (PHP) and Vue.js (Inertia.js). It provides a web-based interface for restaurant staff to manage menu items, process orders, and handle user authentication. The backend leverages Lara<PERSON>'s MVC structure and Eloquent ORM, while the frontend uses Vue 3 and PrimeVue for a modern, interactive UI.

#### 2. Project Structure  
- **app/**: Laravel application code (Controllers, Models, Requests, Resources)
  - **Http/Controllers/**: Handles HTTP requests and business logic
  - **Http/Requests/**: Form validation logic
  - **Http/Resources/**: API resource transformers
  - **Models/**: Eloquent models for database tables
- **routes/**: Route definitions (web.php, auth.php)
- **resources/js/**: Frontend Vue.js code
  - **Pages/**: Vue components for each page (e.g., Dashboard.vue)
  - **app.js**: JS entry point, Inertia/Vue/PrimeVue setup
- **resources/css/**: CSS assets
- **public/**: Publicly accessible files (images, assets)
- **config/**: Laravel configuration files (e.g., database.php)
- **database/**: Migrations, factories, seeders
- **tests/**: PHPUnit tests
  - **Feature/**: HTTP/feature tests
  - **Unit/**: Unit tests
- **.env**: Environment configuration
- **composer.json**: PHP dependencies
- **package.json**: JS dependencies

#### 3. Test Strategy  
- **Framework**: PHPUnit (PHP), Laravel's built-in testing tools
- **Location**: All tests in `tests/` directory
  - **Feature/**: End-to-end and HTTP tests (e.g., authentication, dashboard)
  - **Unit/**: Unit tests for isolated logic
- **Conventions**:
  - Test classes end with `Test.php`
  - Use `RefreshDatabase` trait for DB isolation
  - Factories for model creation
  - Prefer feature tests for user flows, unit tests for logic
- **Mocking**: Use Laravel's built-in mocking and factories
- **Coverage**: Aim for coverage of all controllers, models, and business logic

#### 4. Code Style  
- **PHP**:
  - PSR-12 coding standard
  - Typed properties and return types (PHP 8+)
  - Use Eloquent relationships and query scopes
  - Controller methods: thin, delegate to models/resources
  - Validation via FormRequest classes
  - Consistent naming: `PascalCase` for classes, `camelCase` for methods/variables
  - Docblocks for public methods
  - Use Laravel's logging and error handling
- **JavaScript/Vue**:
  - Composition API (`<script setup>`, `ref`, `computed`)
  - Use PrimeVue for UI components
  - Props for data, emits for events
  - Use `axios` for API calls
  - File names: `PascalCase.vue` for components, `camelCase.js` for modules
  - Use ESLint/Prettier if configured
- **General**:
  - Comment complex logic, but prefer self-explanatory code
  - Use environment variables for secrets/config

#### 5. Common Patterns  
- **MVC**: Controllers for logic, Models for data, Resources for API formatting
- **Eloquent Scopes**: For reusable query logic (e.g., `active()`, `byDepartment()`)
- **Resource Classes**: For consistent API responses
- **Dependency Injection**: For controller/service dependencies
- **Frontend**: Inertia.js for SPA-like navigation, PrimeVue for UI
- **Testing**: Factories for test data, `RefreshDatabase` for isolation

#### 6. Do's and Don'ts  
- ✅ Use FormRequest for validation
- ✅ Use Eloquent relationships and scopes
- ✅ Keep controllers thin, delegate to models/resources
- ✅ Use environment variables for config/secrets
- ✅ Write tests for all new features
- ✅ Use logging for important actions/errors
- ❌ Do not hardcode credentials or secrets
- ❌ Do not put business logic in views
- ❌ Do not bypass validation
- ❌ Do not commit sensitive data

#### 7. Tools & Dependencies  
- **Backend**:
  - Laravel Framework (PHP)
  - Eloquent ORM
  - Inertia.js (Laravel adapter)
  - Laravel Sanctum (API authentication)
  - PHPUnit (testing)
- **Frontend**:
  - Vue 3
  - PrimeVue (UI components)
  - Inertia.js (SPA navigation)
  - Axios (HTTP requests)
  - Tailwind CSS (utility-first CSS)
- **Dev Tools**:
  - Laravel Pint (code style)
  - Faker (test data)
  - Mockery (mocking)
  - Vite (asset bundler)
- **Setup**:
  - Copy `.env.example` to `.env` and configure
  - Run `composer install` and `npm install`
  - Use `php artisan migrate` for DB setup
  - Use `npm run dev` for frontend

#### 8. Other Notes  
- The project uses multiple database connections (see `config/database.php` and `.env`)
- Product images may use custom path logic (see `ProductResource` and `Part` model)
- User authentication is customized (see `LoginController`)
- All API responses should use Resource classes for consistency
- When generating new code, follow Laravel and Vue best practices, and prefer composition over inheritance
