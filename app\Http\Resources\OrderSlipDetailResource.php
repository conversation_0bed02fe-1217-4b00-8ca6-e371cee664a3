<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderSlipDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->ORDERSLIPDETAILID,
            'orderslip_number' => $this->OSNUMBER,
            'orderslip_no' => $this->ORDERSLIPNO,
            'line_no' => $this->LINE_NO,
            'sequence' => $this->SEQUENCE,
            'product_id' => $this->PRODUCT_ID,
            'main_product_id' => $this->MAIN_PRODUCT_ID,
            'main_product_component_id' => $this->MAIN_PRODUCT_COMPONENT_ID,
            'main_product_component_qty' => $this->MAIN_PRODUCT_COMPONENT_QTY,
            'retail_price' => $this->RETAILPRICE ?? 0,
            'quantity' => $this->QUANTITY ?? 0,
            'requested_qty' => $this->REQUESTEDQTY ?? 0,
            'amount' => $this->AMOUNT ?? 0,
            'net_amount' => $this->NETAMOUNT ?? 0,
            'formatted_amount' => $this->formatted_amount,
            'formatted_net_amount' => $this->formatted_net_amount,
            'remarks' => $this->REMARKS,
            'part_no' => $this->PARTNO,
            'order_no' => $this->ORNO,
            'os_type' => $this->OSTYPE,
            'status' => $this->STATUS,
            'customer_code' => $this->CUSTOMERCODE,
            'guest_no' => $this->GUESTNO,
            'guest_type' => $this->GUEST_TYPE,
            'table_no' => $this->TABLENO,
            'is_modify' => (bool) $this->IS_MODIFY,
            'is_group_meal' => (bool) $this->IS_GROUP_MEAL,
            'group_serving' => $this->GROUP_SERVING,
            'product_group' => $this->PRODUCTGROUP,
            'product_status' => $this->PSTATUS,
            'is_tax_exempt' => (bool) $this->IS_TAX_EXEMPT,
            'confirmed_at' => $this->CONFIRMED_AT,
            
            // VAT and discount fields
            'vatable_sales' => $this->VATABLE_SALES ?? 0,
            'vat_amount' => $this->VAT_AMOUNT ?? 0,
            'vat_ex' => $this->VAT_EX ?? 0,
            'sc_discount_percentage' => $this->SC_DISCOUNT_PERCENTAGE ?? 0,
            'sc_discount_amount' => $this->SC_DISCOUNT_AMOUNT ?? 0,
            'sc_count' => $this->SC_COUNT ?? 0,
            'reg_count' => $this->REG_COUNT ?? 0,
            
            // Meal stub information
            'meal_stub_product_id' => $this->MEAL_STUB_PRODUCT_ID,
            'meal_stub_serial_number' => $this->MEAL_STUB_SERIAL_NUMBER,
            'postmix_id' => $this->POSTMIXID,
            
            // System fields
            'branch_id' => $this->BRANCHID,
            'outlet_id' => $this->OUTLETID,
            'device_no' => $this->DEVICENO,
            'location_id' => $this->LOCATIONID,
            'display_monitor' => $this->DISPLAYMONITOR,
            'pos_line_no' => $this->POSLINENO,
            'dev_mod' => $this->DEV_MOD,
            'os_sc_id' => $this->OS_SC_ID,
            'encoded_date' => $this->ENCODEDDATE,
            'os_date' => $this->OSDATE,
            
            // Product relationship (when loaded)
            'product' => new ProductResource($this->whenLoaded('product')),
            
            // Computed properties
            'total_line_amount' => $this->AMOUNT * $this->QUANTITY,
            'unit_price' => $this->QUANTITY > 0 ? $this->AMOUNT / $this->QUANTITY : 0,
        ];
    }
}