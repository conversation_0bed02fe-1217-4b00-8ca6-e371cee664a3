<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

try {
    echo "Debugging product data...\n";
    
    // Check distinct values in Delisted column
    $delistedValues = App\Models\Product::select('Delisted')
        ->distinct()
        ->pluck('Delisted')
        ->toArray();
    
    echo "Distinct Delisted values: " . implode(', ', $delistedValues) . "\n";
    
    // Get a few sample products to see their data
    $sampleProducts = App\Models\Product::take(5)->get();
    
    echo "\nSample products:\n";
    foreach ($sampleProducts as $product) {
        echo "ID: {$product->Product_ID}, Name: {$product->Product_Desc}, Price: {$product->Product_Price}, Delisted: {$product->Delisted}\n";
    }
    
    // Try getting products without the active scope
    $allProductsCount = App\Models\Product::count();
    echo "\nTotal products (without scope): " . $allProductsCount . "\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}