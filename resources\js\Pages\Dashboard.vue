<template>
    <div class="min-h-screen bg-gray-100">
        <!-- Navigation -->
        <NavBar 
            :user="props.user"
            :cartItemsCount="cartItemsCount"
            @nav-table="() => {}"
            @nav-orderslips="goToOrderslips"
            @nav-cart="goToCart"
            @logout="logout"
        />

       <div class="w-full mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- <div class="grid grid-cols-1 lg:grid-cols-3 gap-8"> -->
                <!-- Left Side: Product List -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-lg shadow p-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Menu Items</h2>

                        <!-- Loading State -->
                        <div v-if="loading" class="flex justify-center items-center py-12">
                            <ProgressSpinner />
                        </div>

                        <!-- Products Grid -->
                        <div v-else class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                            <Card v-for="product in products" :key="product.id"
                                class="shadow-md hover:shadow-lg transition-shadow cursor-pointer"
                                @click="openDialog(product)">
                                <template #header>
                                    <img v-if="product.image" :src="product.image" :alt="product.name"
                                    class="w-full h-48 object-cover" />
                                </template>
                                <template #title>
                                    <h3 class="text-lg font-semibold text-gray-900">{{ product.name }}</h3>
                                </template>
                                <template #content>
                                    <p class="text-gray-700 text-sm mb-4">{{ product.description }}</p>
                                    <div class="flex justify-between items-center">
                                        <span class="text-2xl font-bold text-green-600">P{{ product.price.toFixed(2)
                                            }}</span>
                                    </div>
                                </template>
                            </Card>
                        </div>
                 
                        <Dialog v-model:visible="showDialog" modal header="Add to Cart" :style="{ width: '500px' }">
                            <div v-if="selectedProduct">
                                <div class="mb-4">
                                    <div class="font-semibold text-lg mb-2">{{ selectedProduct.name }}</div>
                                    <div class="mb-2">Price: <span class="font-bold">P{{ selectedProduct.price.toFixed(2) }}</span></div>
                                    <div class="flex items-center space-x-2">
                                        <label for="quantity" class="block">Quantity:</label>
                                        <InputNumber v-model="dialogQuantity" inputId="quantity" :min="1" :max="999" />
                                    </div>
                                </div>
                                <Button label="Add to Cart" icon="pi pi-cart-plus" class="w-full" @click="confirmAddToCart" />
                            </div>
                        </Dialog>

                        <!-- Empty State -->
                        <div v-if="!loading && products.length === 0" class="text-center py-12">
                            <i class="pi pi-shopping-cart text-6xl text-gray-300 mb-4"></i>
                            <h3 class="text-xl font-semibold text-gray-600 mb-2">No products available</h3>
                            <p class="text-gray-500">Check back later for new menu items.</p>
                        </div>
                    </div>
                </div>

                <!-- Right Side: Cart/Selection Panel -->
                <!-- <div class="lg:col-span-1">
                    <div class="bg-white rounded-lg shadow p-16 sticky top-8 ">
                        <div class="flex justify-between items-center mb-6">
                            <h2 class="text-xl font-bold text-gray-900">Transaction Summary</h2>
                            <Badge :value="cartItemsCount" severity="info" />
                        </div> -->

                        <!-- Cart Items -->
                        <!-- <div v-if="cartItems.length > 0" class="space-y-4">
                            <div v-for="item in cartItems" :key="item.id"
                                class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                <div class="flex-1">
                                    <h4 class="font-medium text-gray-900">{{ item.name }}</h4>
                                    <p class="text-sm text-gray-600">P{{ item.price.toFixed(2) }} each</p>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <Button icon="pi pi-minus" size="small" severity="secondary" outlined
                                        @click="decreaseQuantity(item.id)" />
                                    <span class="font-semibold min-w-[2rem] text-center">{{ item.quantity }}</span>
                                    <Button icon="pi pi-plus" size="small" severity="secondary" outlined
                                        @click="increaseQuantity(item.id)" />
                                </div>
                            </div> -->

                            <!-- Cart Summary -->
                            <!-- <Divider />
                            <div class="space-y-2">
                                <div class="flex justify-between text-lg font-semibold">
                                    <span>Total:</span>
                                    <span class="text-green-600">${{ cartTotal.toFixed(2) }}</span>
                                </div>
                                <Button label="Checkout" icon="pi pi-credit-card" class="w-full" severity="success"
                                    @click="checkout" />
                                <Button label="Clear Cart" icon="pi pi-trash" class="w-full" severity="danger" outlined
                                    @click="clearCart" />
                            </div>
                        </div> -->

                        <!-- Empty Cart -->
                        <!-- <div v-else class="text-center py-8">
                            <i class="pi pi-shopping-cart text-4xl text-gray-300 mb-4"></i>
                            <h3 class="text-lg font-semibold text-gray-600 mb-2">Your cart is empty</h3>
                            <p class="text-gray-500 text-sm">Add some delicious items from the menu!</p>
                        </div>
                    </div>
                </div> -->
            <!-- </div> -->
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { router } from '@inertiajs/vue3'
import axios from 'axios'
import Card from 'primevue/card'
import Button from 'primevue/button'
import Badge from 'primevue/badge'
import ProgressSpinner from 'primevue/progressspinner'
import Divider from 'primevue/divider'
import Dialog from 'primevue/dialog'
import InputNumber from 'primevue/inputnumber'
import NavBar from '../Components/NavBar.vue'

// Define props to receive user data from the controller
const props = defineProps({
    user: {
        type: Object,
        required: true
    },
    products: {
        type: Array,
        default: () => []
    }
})

// Reactive data
const loading = ref(false)
const products = ref([])
const cartItems = ref([])

// Dialog state
const showDialog = ref(false)
const selectedProduct = ref(null)
const dialogQuantity = ref(1)

// Computed properties
const cartItemsCount = computed(() => {
    return cartItems.value.reduce((total, item) => total + item.quantity, 0)
})

const cartTotal = computed(() => {
    return cartItems.value.reduce((total, item) => total + (item.price * item.quantity), 0)
})

// Cart management functions
const loadCartFromStorage = () => {
    try {
        const savedCart = localStorage.getItem('restaurant_cart')
        if (savedCart) {
            cartItems.value = JSON.parse(savedCart)
        }
    } catch (error) {
        console.error('Error loading cart from storage:', error)
        cartItems.value = []
    }
}

const saveCartToStorage = () => {
    try {
        localStorage.setItem('restaurant_cart', JSON.stringify(cartItems.value))
    } catch (error) {
        console.error('Error saving cart to storage:', error)
    }
}

// Navigation functions
const goToCart = () => {
    router.get('/cart')
}

const goToOrderslips = () => {
    router.get('/pending-orderslips')
}

// Methods
const fetchProducts = async () => {
    loading.value = true;
    try {
        // Fetch from ProductController@index, which returns { success, data: [ProductResource] }
        const response = await axios.get('/products');
        if (response.data && response.data.success && Array.isArray(response.data.data)) {
            // Map ProductResource fields to the expected frontend format
            products.value = response.data.data.map(product => ({
                id: product.product_id,
                name: product.product_description,
                description: product.product_description,
                price: product.price,
                category: product.department_id || 'Uncategorized',
                image: product.img_path && product.img_path !== '/assets/default-product.png' ? product.img_path : null,
                available: !product.delisted,
                quick_code: product.quick_code,
                serving: product.serving,
                is_group_meal: product.is_group_meal,
                printer_location: product.printer_location,
            }));
        } else {
            products.value = [];
        }
    } catch (error) {
        console.error('Error fetching products:', error);
        products.value = [];
    } finally {
        loading.value = false;
    }
}

const openDialog = (product) => {
    selectedProduct.value = product
    dialogQuantity.value = 1
    showDialog.value = true
}

const confirmAddToCart = () => {
    if (!selectedProduct.value) return
    const product = selectedProduct.value
    const quantity = dialogQuantity.value
    const existingItem = cartItems.value.find(item => item.id === product.id)
    if (existingItem) {
        existingItem.quantity += quantity
    } else {
        cartItems.value.push({
            id: product.id,
            name: product.name,
            price: product.price,
            quantity: quantity
        })
    }
    saveCartToStorage()
    showDialog.value = false
    // Optional: Show success message or animation
    console.log(`Added ${quantity} x ${product.name} to cart`)
}

const increaseQuantity = (productId) => {
    const item = cartItems.value.find(item => item.id === productId)
    if (item) {
        item.quantity += 1
    }
}

const decreaseQuantity = (productId) => {
    const item = cartItems.value.find(item => item.id === productId)
    if (item) {
        if (item.quantity > 1) {
            item.quantity -= 1
        } else {
            // Remove item if quantity becomes 0
            cartItems.value = cartItems.value.filter(item => item.id !== productId)
        }
    }
}

const clearCart = () => {
    cartItems.value = []
}

const checkout = () => {
    if (cartItems.value.length === 0) {
        alert('Your cart is empty!')
        return
    }

    // Here you would typically send the cart data to your backend
    // For now, we'll just show an alert and clear the cart
    const orderData = {
        items: cartItems.value,
        total: cartTotal.value,
        user_id: props.user.id || props.user.ID
    }

    console.log('Processing order:', orderData)

    // You can use Inertia to submit the order
    router.post('/orders', orderData, {
        onSuccess: () => {
            alert('Order placed successfully!')
            clearCart()
        },
        onError: (errors) => {
            console.error('Order failed:', errors)
            alert('Failed to place order. Please try again.')
        }
    })
}

// No image error handler needed since we don't use a placeholder

// Logout function
const logout = () => {
    router.post('/logout', {}, {
        onSuccess: () => {
            // Redirect will be handled by the controller
        }
    })
}

// Lifecycle hooks
onMounted(() => {
    fetchProducts()
    loadCartFromStorage()
})
</script>