<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Carbon\Carbon;

class OrderSlipResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->ORDERSLIPNO,
            'orderslip_code' => $this->OSNUMBER,
            'orderslip_number' => $this->ORDERSLIPNO,
            'branch_id' => $this->BRANCHID,
            'outlet_id' => $this->OUTLETID,
            'device_no' => $this->DEVICENO,
            'table_number' => $this->QUEUEDNAME,
            'total_amount' => $this->TOTALAMOUNT ?? 0,
            'net_amount' => $this->NETAMOUNT ?? 0,
            'is_paid' => (bool) $this->PAID,
            'status' => $this->STATUS,
            'os_type' => $this->OSTYPE,
            'os_type_text' => $this->getOsTypeText(),
            'is_active' => (bool) $this->ISACTIVE,
            'user_current_transaction' => $this->USER_CURRENT_TRANSACTION,
            'encoded_by' => $this->ENCODEDBY,
            'prepared_by' => $this->PREPAREDBY,
            'customer_name' => $this->CCENAME,
            'created_at' => $this->formatDateTime($this->OSDATE),
            'created_date' => $this->created_date,
            'created_time' => $this->created_time,
            'encoded_date' => $this->formatDateTime($this->ENCODEDDATE),
            'completed_at' => $this->formatDateTime($this->QDATE),
            'service_charge_amount' => $this->SERVICE_CHARGE_AMT ?? 0,
            'service_charge_percentage' => $this->SERVICE_CHARGE_PERCENTAGE ?? 0,
            'is_senior_citizen' => (bool) $this->IS_SC,
            'account_type' => $this->ACCOUNTTYPE,
            'is_confirmed' => $this->ACCOUNTTYPE > 0,
            
            // Relationship data (when loaded)
            // 'details' => OrderSlipDetailResource::collection($this->whenLoaded('details')),
            'details_count' => $this->whenLoaded('details', function () {
                return $this->details->count();
            }),
            'items_count' => $this->whenLoaded('details', function () {
                return $this->details->sum('QUANTITY');
            }),
            
            // Computed properties
            'duration' => $this->calculateDuration(),
            'status_badge' => $this->getStatusBadge(),
        ];
    }

    /**
     * Get the OS type text representation.
     */
    private function getOsTypeText(): string
    {
        switch ($this->OSTYPE) {
            case 1:
                return 'DINE IN';
            case 2:
                return 'TAKE OUT';
            default:
                return 'UNKNOWN';
        }
    }

    /**
     * Format datetime for frontend display.
     */
    private function formatDateTime($datetime): ?string
    {
        if (!$datetime) {
            return null;
        }

        try {
            return Carbon::parse($datetime)->toISOString();
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Calculate duration from creation to completion.
     */
    private function calculateDuration(): ?string
    {
        if (!$this->OSDATE) {
            return null;
        }

        $start = Carbon::parse($this->OSDATE);
        $end = $this->QDATE ? Carbon::parse($this->QDATE) : Carbon::now();

        $diff = $start->diff($end);

        if ($diff->h > 0) {
            return $diff->format('%h hr %i min');
        } elseif ($diff->i > 0) {
            return $diff->format('%i min');
        } else {
            return $diff->format('%s sec');
        }
    }

    /**
     * Get status badge information.
     */
    private function getStatusBadge(): array
    {
        if ($this->PAID) {
            return [
                'text' => 'Paid',
                'color' => 'success',
                'class' => 'bg-green-500'
            ];
        }

        if ($this->USER_CURRENT_TRANSACTION) {
            return [
                'text' => 'In Progress',
                'color' => 'info',
                'class' => 'bg-blue-500'
            ];
        }

        return [
            'text' => 'Pending',
            'color' => 'warning',
            'class' => 'bg-yellow-500'
        ];
    }
}