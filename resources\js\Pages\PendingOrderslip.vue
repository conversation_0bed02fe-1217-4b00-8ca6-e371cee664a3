<template>
  <div class="min-h-screen bg-gray-100">
    <!-- NAVBAR -->
    <NavBar
      :user="props.auth.user"
      :cartItemsCount="cartItemsCount"
      @nav-table="() => {}"
      @nav-orderslips="goToOrderslips"
      @nav-dashboard="goToDashboard"
      @nav-cart="goToCart"
      @logout="logout"
    />

    <div class="max-w-8xs mx-auto py-6 mb-16 pb-4 px-4 sm:py-10 sm:px-6 lg:px-8">
      <!-- LEGEND -->
      <div class="flex justify-center mb-2">
        <div class="flex items-center text-xs font-semibold">
          <div class="w-4 h-4 bg-green-500 rounded-sm"></div>
          <span class="ml-1"> Paid </span>
        </div>
        <div class="ml-3 flex items-center text-xs font-semibold">
          <div class="w-4 h-4 bg-yellow-500 rounded-sm"></div>
          <span class="ml-1"> Unpaid </span>
        </div>
      </div>

      <div class="mt-2 mb-6 flex justify-between items-center">
        <Button
          label="New Transaction"
          icon="pi pi-plus"
          class="p-button-success"
          @click="onNewTransaction"
        />

        <div class="flex items-center space-x-2">
          <InputText v-model="searchQuery" placeholder="Search..." class="p-inputtext-sm" @keyup.enter="refetchOrderslips()"/>
          <Button icon="pi pi-search" class="p-button-outlined p-button-sm" @click="refetchOrderslips()" />
        </div>
      </div>

      <div v-if="loading" class="flex justify-center pt-16 pb-14">
        <ProgressSpinner/>
      </div>
      <div v-else>
        <div v-if="orderslips && orderslips.data.length > 0">
          <div class="grid grid-cols-2 sm:gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 ">
            <Card v-for="item in orderslips.data" :key="item.orderslip_number" class="relative group">
            <template #header>
            <div :class="['h-8 flex items-center justify-center', item.is_paid ? 'bg-green-500' : 'bg-yellow-500']">
            <span class="text-white font-bold">{{ item.orderslip_number }}</span>
            <span v-if="isActiveTransaction(item)" class="ml-2"><Badge value="Active" severity="info" /></span>
            </div>
            </template>
              <template #content>
                <div class="mb-2">
                  <b>Status:</b>
                  <span :class="item.is_paid ? 'text-green-700' : 'text-yellow-700'">{{ item.is_paid ? 'Paid' : 'Unpaid' }}</span>
                </div>
                <div class="mb-2"><b>Created:</b> {{ item.created_date }} {{ item.created_time }}</div>
                <div><b>By:</b> {{ item.user_current_transaction || '-' }}</div>
              </template>
              <template #footer>
                <div class="flex flex-row flex-wrap gap-2">
                  <Button v-if="!isActiveTransaction(item)" class="p-button-info" label="Attach" size="small" @click="() => attachToTransaction(item)" />
                  <Button v-else severity="secondary" label="Detach" size="small" @click="() => detachTransaction(item)" />
                  <Button class="p-button-secondary" label="View" size="small" icon="pi pi-eye" @click="() => openOSOverview(item.orderslip_number)" />
                </div>
              </template>
            </Card>
          </div>
          <div class="flex items-center justify-between mt-6">
            <span class="text-xs text-gray-600">
              Showing {{ orderslips.meta.from }} to {{ orderslips.meta.to }} of {{ orderslips.meta.total }}
            </span>
            <Paginator :rows="paging.limit"
              :totalRecords="orderslips.meta.total"
              :first="(orderslips.meta.current_page - 1) * orderslips.meta.per_page"
              @page="onPageChange"
              class="mt-2"
            />
          </div>
        </div>
        <div v-else class="flex flex-col items-center pt-16 pb-10">
          <i class="pi pi-inbox text-5xl text-gray-400 mb-4"></i>
          <p class="text-gray-600 font-medium">No pending orderslips found.</p>
          <Button label="Retry" icon="pi pi-refresh" class="mt-3" @click="refetchOrderslips()"/>
        </div>
      </div>

      <!-- Orderslip Overview Dialog  -->
      <Dialog v-model:visible="osOverviewVisible" header="Orderslip Overview" :style="{ width: '75vw' }" :modal="true">
        <OrderslipOverview :orderslip-code="selectedOrderslipCode" @transaction-updated="refetchOrderslips"/>
      </Dialog>
      <ConfirmDialog/>
      <Toast/>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import axios from 'axios';
import { useToast } from 'primevue/usetoast';
import { useConfirm } from 'primevue/useconfirm';
import OrderslipOverview from './OrderslipOverview.vue';
import NavBar from '../Components/NavBar.vue';

import {
  Button, Card, Badge, Toast, ConfirmDialog, Paginator, Dialog, ProgressSpinner, InputText
} from 'primevue';

const props = defineProps({
  auth: { type: Object, required: true },

});

const paging = ref({ page: 1, limit: 25 });
const searchQuery = ref('');
const orderslips = ref(null);
const loading = ref(false);
const osOverviewVisible = ref(false);
const selectedOrderslipCode = ref();
const cartItemsCount = ref(0); 
const toast = useToast();
const confirm = useConfirm();
const isActiveTransaction = (item) => {
  return item.user_current_transaction && item.user_current_transaction == props.auth.user.id;
};


const fetchOrderslips = async () => {
  loading.value = true;
  try {
    const response = await axios.get('/orderslips', {
      params: {
        search: searchQuery.value,
        page: paging.value.page,
        limit: paging.value.limit,
        hide_completed: true
      }
    });
    orderslips.value = response.data;
  } catch (error) {
    toast.add({ severity: 'error', summary: 'Error', detail: 'Failed to load orderslips', life: 3500 });
    orderslips.value = { data: [] };
  }
  loading.value = false;
};

const refetchOrderslips = () => {
  paging.value.page = 1;
  fetchOrderslips();
};

const attachToTransaction = (orderSlip) => {
  confirm.require({
    message: `Attach yourself to orderslip #${orderSlip.orderslip_number}?`,
    header: 'Confirm Attach',
    icon: 'pi pi-exclamation-triangle',
    accept: async () => {
      try {
        loading.value = true;
        await axios.post('/attach-current-transaction', { orderslip_code: orderSlip.orderslip_code });
        toast.add({ severity: 'success', summary: 'Attached!', detail: `You are now assigned to #${orderSlip.orderslip_code}`, life: 2000 });
        window.location.assign('/dashboard');
      } catch (e) {
        toast.add({ severity: 'error', summary: 'Attach Failed', detail: e?.response?.data?.message || 'Could not attach', life: 4000 });
      } finally {
        loading.value = false;
      }
    }
  });
};

const detachTransaction = (orderSlip) => {
  confirm.require({
    message: `Detach yourself from orderslip #${orderSlip.orderslip_number}?`,
    header: 'Confirm Detach',
    icon: 'pi pi-question',
    accept: async () => {
      try {
        loading.value = true;
        await axios.post('/detach-current-transaction', { orderslip_code: orderSlip.orderslip_code });
        toast.add({ severity: 'info', summary: 'Detached', detail: `You have detached from #${orderSlip.orderslip_code}`, life: 2000 });
        fetchOrderslips();
      } catch (err) {
        toast.add({ severity: 'error', summary: 'Detach Failed', detail: err?.response?.data?.message || 'Could not detach', life: 4000 });
      } finally {
        loading.value = false;
      }
    }
  });
};

const onNewTransaction = () => {
  confirm.require({
    message: 'Create a new transaction/orderslip?',
    header: 'New Transaction',
    icon: 'pi pi-plus',
    accept: async () => {
      try {
        loading.value = true;
        const response = await axios.post('/orderslips', {
          user_id: props.auth.user.id,
          user_name: props.auth.user.name,
          service_charge_percentage: props.settings.service_charge_percentage,
        });
        const newOSNumber = response.data.orderslip_code || response.data.osnumber;
        toast.add({ severity: 'success', summary: 'Transaction Created', detail: `Order Slip #${newOSNumber} created.`, life: 2500});
        window.location.assign('/dashboard');
      } catch (e) {
        toast.add({severity: 'error', summary: 'Error', detail: 'Could not create transaction', life: 4000});
      } finally {
        loading.value = false;
      }
    }
  });
};


function openOSOverview(osCode) {
  selectedOrderslipCode.value = osCode;
  osOverviewVisible.value = true;
}

function onPageChange(e) {
  paging.value.page = (typeof e === 'number' ? e + 1 : e.page + 1);
  fetchOrderslips();
}

// --- NAVIGATION ---
function goToOrderslips() { window.location.assign('/pending-orderslips'); }
function goToDashboard() { window.location.assign('/dashboard'); }
function goToCart() { window.location.assign('/cart'); }
function logout() {
  axios.post('/logout')
    .then(() => window.location.href = '/')
    .catch(() => window.location.href = '/');
}

onMounted(() => {
  fetchOrderslips();
});

</script>

<style scoped>
.group:hover .group-hover\:ring-2 {
  --tw-ring-color: #2563eb;
  box-shadow: 0 0 0 2px var(--tw-ring-color);
}
</style>
