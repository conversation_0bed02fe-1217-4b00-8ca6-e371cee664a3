<?php

namespace App\Http\Controllers;

use App\Http\Resources\OrderSlipHeaderCollection;
use App\Http\Resources\OrderSlipResource;
use App\Models\OrderSlipHeader;
use App\Models\OrderSlipDetail;
use App\Models\OrderslipTable;
use App\Http\Services\getCurrentTrasancationService;
use App\Http\Services\RemoveCurrentTransactionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Exception;
use Inertia\Inertia;

class OrderSlipController extends Controller
{
    
    protected getCurrentTrasancationService $getCurrentTransactionService;
    protected RemoveCurrentTransactionService $removeCurrentTransactionService;

    // Use constructor dependency injection so the container provides the services
    public function __construct(
        getCurrentTrasancationService $getCurrentTransactionService,
        RemoveCurrentTransactionService $removeCurrentTransactionService
    ) {
        $this->getCurrentTransactionService = $getCurrentTransactionService;
        $this->removeCurrentTransactionService = $removeCurrentTransactionService;
    }



     public function index(Request $request)
    {
        $items = OrderSlipHeader::with('terminal')
            ->when($request->branch_id, function ($q) use ($request){
                $q->where('BRANCHID', $request->branch_id);
            })
            ->when($request->outlet_id, function ($q) use ($request) {
                $q->where('OUTLETID', $request->outlet_id);
            })
            ->when($request->hide_completed, function ($q) use ($request) {
                $q->where('QDATE', '=', null);
            })
            ->when($request->show_completed === true, function ($q) use ($request) {
                $q->where('QDATE', '!=', null);
            })
            ->orderBy('OSDATE', 'desc')
            ->paginate($request->limit ?? 25);
            
        return new OrderSlipHeaderCollection($items);
    }
    public function pendingOrderslipsPage(Request $request) {
    return Inertia::render('PendingOrderslip', [
        'settings' => config('settings'),
        'auth' => [
            'user' => Auth::user()
        ]
    ]);
    }

   public function store(Request $request)
{
    $request->validate([
        'branch_id' => 'required|integer',
        'outlet_id' => 'required|integer',
        'device_id' => 'required|integer',
        'user_id'   => 'required|integer',
        'user_name' => 'required|string',
    ]);

    try {
        DB::beginTransaction();

        $result = $this->getCurrentTransactionService->handle(
            $request->branch_id,
            $request->user_id
        );

        if (!empty($result['success'])) {
            $this->removeCurrentTransactionService->handle(
                $request->branch_id,
                $request->user_id
            );
        }

        OrderSlipHeader::where('DEVICENO', $request->device_id)
            ->where('BRANCHID', $request->branch_id)
            ->where('CUSTOMERDISPLAY', 1)
            ->update(['CUSTOMERDISPLAY' => 0]);

        // Generate IDs & timestamps
        $orderslip_new_id = OrderslipHeader::getNewID($request->branch_id, $request->outlet_id, $request->device_id);
        $osnumber = $this->generateOrderSlipNumber($orderslip_new_id);
        $currentDateTime = now();
        $clarionDate = getClarionDate($currentDateTime);

        // Build payload
        $payload = [
            'ORDERSLIPNO' => $orderslip_new_id,
            'BRANCHID' => $request->branch_id,
            'OUTLETID' => $request->outlet_id,
            'DEVICENO' => $request->device_id,
            'STATUS' => 'X',
            'DISPLAYMONITOR' => 2,
            'ENCODEDBY' => $request->user_id,
            'PREPAREDBY' => $request->user_name,
            'CCENAME' => $request->user_name,
            'TRANSACTTYPEID' => 1,
            'OSDATE' => $currentDateTime,
            'ORIGINALINVOICEDATE' => $clarionDate,
            'ENCODEDDATE' => $currentDateTime,
            'OSNUMBER' => $osnumber,
            'USER_CURRENT_TRANSACTION' => $request->user_id,
            'BUSDATE' => $clarionDate,
            'PAID' => 0,
            'ACCOUNTTYPE' => 0,
        ];

        if (config('settings.app_type') === 'restaurant_ambulant') {
            $payload = array_merge($payload, [
                'OSTYPE' => config('settings.default_order_type'),
                'SERVICE_CHARGE_AMT' => 0,
                'SERVICE_CHARGE_PERCENTAGE' =>
                    config('settings.default_order_type') == 1
                    ? config('settings.service_charge_percentage')
                    : 0,
            ]);
        }

        OrderSlipHeader::create($payload);

        DB::commit();

        return response()->json([
            'message' => 'Ok',
            'osnumber' => $osnumber,
        ]);

    } catch (Exception $e) {
        DB::rollBack();
        return response()->json([
            'message' => 'Server error while creating Order Slip',
            'error'   => $e->getMessage()
        ], 500);
    }
}

    public function show($orderslip_code)
    {
        try {
            $orderSlip = OrderSlipHeader::where('OSNUMBER', $orderslip_code)
                ->with(['items', 'payments'])
                ->firstOrFail();

            return response()->json([
                'message' => 'Order slip details',
                'data' => $orderSlip
            ]);
        } catch (Exception $e) {
            return response()->json([
                'message' => 'Order slip not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }


}