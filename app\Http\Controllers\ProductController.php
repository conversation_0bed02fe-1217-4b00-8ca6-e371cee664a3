<?php

namespace App\Http\Controllers;

use App\Http\Resources\ProductResource;
use App\Models\Product;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    
    public function index(Request $request)
    {
        $query = Product::query();

        // If department is selected, filter by department
        if ($request->has('department_id') && $request->department_id !== '') {
            $query->where('Department_ID', $request->department_id);
        }

        // If search term exists, add search condition
        if ($request->has('search') && $request->search !== '') {
            $query->where('Product_Desc', 'LIKE', '%' . $request->search . '%');
        }

        $products = $query->get();

        return response()->json([
            'success' => true,
            'data' => ProductResource::collection($products),
        ]);
    }

     public function product(Request $request ) {

        $res = Product::leftJoin('parts', 'products.part_number','=', 'parts.part_number')
            ->where('products.part_number', $request->part_number)
            ->first();
        return $res;
    }
}
