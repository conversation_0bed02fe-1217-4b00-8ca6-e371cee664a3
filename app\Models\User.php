<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{

  use HasFactory, Notifiable;
  
  
    protected $table = 'Cashiers';
    protected $primaryKey = 'Cashier_ID';
    protected $connection = 'datalogic';

    protected $fillable = [
        'Cashier_ID',
        'Cashier_Number',
        'Cashier_Name',
        'Cashier_Psw',
        'Cashier_Role_ID',
        'Cashier_Active'
    ];


      public function role()
    {
        return $this->belongsTo(CashierRole::class, 'Cashier_Role_ID', 'Cashier_Role_ID');
    }

    public function isActive()
    {
        return $this->Cashier_Active == 1;
    }

}
