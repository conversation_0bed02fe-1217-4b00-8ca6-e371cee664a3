<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    protected $connection = 'sqlsrv';
    protected $table = 'Products';
    protected $primaryKey = 'Product_ID';
    public $timestamps = false;

    protected $fillable = [
        'Product_ID',
        'Tax_ID',
        'Department_ID',
        'Product_Desc',
        'Product_Price',
        'Product_Quick_Code',
        'Dept_Tax_Exempt_Qty',
        'SCVat',
        'Discount_Rate',
        'Delisted',
        'isGroupMeal',
        'Serving',
        'PrinterLocation', // Fixed typo from PrinterLocation to PrinterLocation
    ];

    public function department()
    {
        return $this->belongsTo(Department::class, 'Department_ID');
    }

    public function orderSlipDetails()
    {
        return $this->hasMany(OrderSlipDetail::class, 'PRODUCT_ID');
    }

    // Scopes as arrow functions
    public function scopeActive($query) {
        return $query->where('Delisted', '=', 0);
    } 
    public function scopeSearch($query, $searchTerm) {
        return  $query->where('Product_Desc', 'LIKE', "%$searchTerm%");
    }
     
    public function scopeByDepartment($query, $departmentId) {
        return $query->where('Department_ID', $departmentId);
    }

    
    public function scopeGroupMeals($query) {
        $query->where('isGroupMeal', 1);
    }
    public function scopeWithServing($query){
        $query->whereNotNull('Serving');
    }
    public function scopeBar($query){ return $query->where('PrinterLocation', 'B');
    }
    public function scopeByPrinterLocation($query, $location) {
     return $query->where('PrinterLocation', $location);
    }
    // Methods with return types
    public function isActive(): bool
    {
        return $this->Delisted == 0;
    }

    public function isKitchen(): bool
    {
        return $this->PrinterLocation === 'K';
    }

    public function isBar(): bool
    {
        return $this->PrinterLocation === 'B';
    }

    // Typed properties (PHP 8.0+)
    public function getPrinterLocationDescriptionAttribute(): string
    {
        return match($this->PrinterLocation) {
            'K' => "Kitchen",
            'B' => "Bar",
            default => "",
        };
    }

    public function getFormattedPriceAttribute(): string
    {
        return number_format($this->Product_Price, 2);
    }
}