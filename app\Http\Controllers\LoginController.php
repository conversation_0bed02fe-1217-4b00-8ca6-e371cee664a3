<?php

namespace App\Http\Controllers;

use App\Http\Requests\Auth\LoginRequest;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Http\Resources\User as ResourcesUser;

class LoginController extends Controller
{
    public function index()
    {
        return Inertia::render('Login', []);
    }

    public function login(LoginRequest $request)
    {
        Log::info('Login attempt', ['username' => $request->username]);
        $cashier = User::where('Cashier_Number', $request->username)
            ->where('Cashier_Active', 1)
            ->first();

        if (is_null($cashier)) {
            Log::warning('Login failed: user not found', ['username' => $request->username]);
            return back()->withErrors([
                'username' => 'User not found'
            ])->withInput();
        }

        if (trim($cashier->Cashier_Psw) != $request->password) {
            Log::warning('Login failed: invalid password', ['username' => $request->username]);
            return back()->withErrors([
                'password' => 'Invalid Password'
            ])->withInput();
        }

        Auth::login($cashier);
        Log::info('Login successful', ['user_id' => $cashier->Cashier_ID]);

        return redirect()->intended(route('pending-orderslips'));
    }

    public function logout(Request $request)
    {
        Auth::logout();
        
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('login');
    }

    public function verifySupervisor(Request $request)
    {
        try {
            $request->validate([
                'username' => 'required|string',
                'password' => 'required|string'
            ]);
            
            // Find Cashier provided username
            $supervisor = User::where('Cashier_Number', $request->username)
                ->where('Cashier_Active', 1)
                ->first();

            if (!$supervisor) {
                return response()->json([
                    'success' => false,
                    'message' => 'Supervisor not found'
                ], 400);
            }

            if (trim($supervisor->Cashier_Psw) != $request->password) {
                return response()->json([
                    'success' => false,
                    'message' => 'Incorrect password'
                ], 400);
            }

            if ($supervisor->Cashier_Role_ID > 2) {
                return response()->json([
                    'success' => false,
                    'message' => 'You are not a supervisor'
                ], 403);
            }

            return response()->json([
                'success' => true,
                'message' => 'Supervisor verified successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Verification failed: ' . $e->getMessage()
            ], 500);
        }
    }
}